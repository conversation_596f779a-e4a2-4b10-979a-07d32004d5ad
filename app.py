from datetime import datetime
from flask import Flask, request, jsonify
from classifier import SoftwareClassifier

# Configuración de Flask
app = Flask(__name__)

# Inicializar el clasificador de software (ajusta la ruta según tu configuración)
MODELS_PATH = 'models'
software_classifier = None

def initialize_classifier():
    """Inicializa el clasificador al arrancar la aplicación"""
    global software_classifier
    try:
        software_classifier = SoftwareClassifier(MODELS_PATH)
        print("🚀 API lista para recibir peticiones!")
    except Exception as e:
        print(f"❌ Error inicializando clasificador: {str(e)}")

@app.route('/predict', methods=['POST'])
def predict_both():
    """Predice tanto categoría como nombre comercial"""
    try:
        data = request.json
        name = data.get('name', '')
        vendor = data.get('vendor', '')
        
        if not name or not vendor:
            return jsonify({'error': 'Se requieren tanto name como vendor'}), 400
        
        predictions = software_classifier.predict_both(name, vendor)
        
        return jsonify({
            'category': predictions['category'],
            'comercial_name': predictions['comercial_name']
        })
        
    except Exception as e:
        return jsonify({'error': f'Error en predicción: {str(e)}'}), 500

@app.route('/predict/batch', methods=['POST'])
def predict_batch():
    """Predice categoría y nombre comercial para una lista de productos"""
    try:
        data = request.json
        items = data.get('items', [])
        
        if not items:
            return jsonify({'error': 'Se requiere una lista de items no vacía'}), 400
        
        predictions = []
        for item in items:
            name = item.get('name', '')
            vendor = item.get('vendor', '')
            
            if not name or not vendor:
                predictions.append({
                    'category': '',
                    'comercial_name': ''
                })
            else:
                result = software_classifier.predict_both(name, vendor)
                predictions.append({
                    'category': result['category'],
                    'comercial_name': result['comercial_name']
                })
        
        return jsonify({'predictions': predictions})
        
    except Exception as e:
        return jsonify({'error': f'Error en predicción batch: {str(e)}'}), 500

@app.route('/predict/category', methods=['POST'])
def predict_category():
    """Predice solo la categoría"""
    try:
        data = request.json
        name = data.get('name', '')
        vendor = data.get('vendor', '')
        
        if not name or not vendor:
            return jsonify({'error': 'Se requieren tanto name como vendor'}), 400
        
        category = software_classifier.predict_category(name, vendor)
        
        return jsonify({
            'category': category,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'Error en predicción: {str(e)}'}), 500

@app.route('/predict/commercial', methods=['POST'])
def predict_comercial():
    """Predice solo el nombre comercial"""
    try:
        data = request.json
        name = data.get('name', '')
        vendor = data.get('vendor', '')
        
        if not name or not vendor:
            return jsonify({'error': 'Se requieren tanto name como vendor'}), 400
        
        comercial_name = software_classifier.predict_comercial_name(name, vendor)
        
        return jsonify({
            'comercial_name': comercial_name,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'Error en predicción: {str(e)}'}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de salud de la API"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'classifier_ready': software_classifier is not None and software_classifier.is_ready()
    })

if __name__ == '__main__':
    # Inicializar clasificador
    initialize_classifier()
    
    # Ejecutar la API
    app.run(debug=True, host='0.0.0.0', port=5000)