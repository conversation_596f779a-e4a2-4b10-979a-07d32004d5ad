#!/usr/bin/env python3
"""
Script to download ML models from S3.
Can be used during Docker build or for local development.
"""

import os
import json
import sys
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from pathlib import Path
import time

def load_env_file(env_file='.env'):
    """Load environment variables from .env file if it exists."""
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print(f"✓ Loaded environment variables from {env_file}")

def setup_s3_client():
    """Initialize S3 client with credentials."""
    try:
        # Try to create S3 client (will use env vars, IAM role, or AWS profile)
        s3_client = boto3.client('s3')
        
        # Test credentials by listing buckets
        s3_client.list_buckets()
        print("✓ S3 credentials configured successfully")
        return s3_client
        
    except NoCredentialsError:
        print("❌ AWS credentials not found. Please configure:")
        print("   1. Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables")
        print("   2. Or configure AWS CLI: aws configure")
        print("   3. Or use IAM roles if running on AWS")
        sys.exit(1)
    except ClientError as e:
        print(f"❌ AWS client error: {e}")
        sys.exit(1)

def download_file_from_s3(s3_client, bucket_name, s3_key, local_path):
    """Download a single file from S3."""
    try:
        # Create local directory if it doesn't exist
        local_path.parent.mkdir(parents=True, exist_ok=True)
        
        print(f"  Downloading {s3_key}...")
        start_time = time.time()
        
        s3_client.download_file(bucket_name, s3_key, str(local_path))
        
        # Verify file was downloaded
        if local_path.exists():
            file_size = local_path.stat().st_size / (1024 * 1024)  # MB
            duration = time.time() - start_time
            print(f"  ✓ Downloaded {local_path.name} ({file_size:.1f}MB) in {duration:.1f}s")
            return True
        else:
            print(f"  ❌ Failed to download {local_path.name}")
            return False
            
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'NoSuchKey':
            print(f"  ❌ File not found in S3: {s3_key}")
        elif error_code == 'NoSuchBucket':
            print(f"  ❌ Bucket not found: {bucket_name}")
        else:
            print(f"  ❌ S3 error downloading {s3_key}: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error downloading {s3_key}: {e}")
        return False

def load_manifest(manifest_path='models_manifest.json'):
    """Load the models manifest file."""
    try:
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        print(f"✓ Loaded manifest with {len(manifest['models'])} models")
        return manifest
    except FileNotFoundError:
        print(f"❌ Manifest file not found: {manifest_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in manifest file: {e}")
        sys.exit(1)

def main():
    print("🚀 Starting model download from S3...")
    
    # Load environment variables from .env file if it exists
    load_env_file()
    
    # Get S3 configuration from environment
    bucket_name = os.getenv('S3_BUCKET_NAME')
    if not bucket_name:
        print("❌ S3_BUCKET_NAME environment variable not set")
        sys.exit(1)
    
    # Initialize S3 client
    s3_client = setup_s3_client()
    
    # Load manifest
    manifest = load_manifest()
    
    # Create models directory
    models_dir = Path('models')
    models_dir.mkdir(exist_ok=True)
    
    print(f"\n📁 Downloading models to: {models_dir.absolute()}")
    print(f"📦 S3 Bucket: {bucket_name}")
    print(f"📋 Total models to download: {len(manifest['models'])}")
    print(f"💾 Total size: ~{manifest['total_size_mb']}MB\n")
    
    # Download each model
    successful_downloads = 0
    failed_downloads = 0
    
    for model in manifest['models']:
        filename = model['filename']
        s3_key = model['s3_key']
        local_path = models_dir / filename
        
        # Skip if file already exists (useful for caching)
        if local_path.exists():
            print(f"  ⚡ {filename} already exists, skipping...")
            successful_downloads += 1
            continue
        
        success = download_file_from_s3(s3_client, bucket_name, s3_key, local_path)
        if success:
            successful_downloads += 1
        else:
            failed_downloads += 1
    
    # Summary
    print(f"\n📊 Download Summary:")
    print(f"   ✅ Successful: {successful_downloads}")
    print(f"   ❌ Failed: {failed_downloads}")
    
    if failed_downloads > 0:
        print(f"\n❌ {failed_downloads} downloads failed. Check your S3 configuration and file paths.")
        sys.exit(1)
    else:
        print(f"\n🎉 All models downloaded successfully!")
        
        # List downloaded files
        print(f"\n📁 Downloaded files in {models_dir}:")
        for file_path in sorted(models_dir.glob('*.pkl')):
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"   {file_path.name} ({size_mb:.1f}MB)")

if __name__ == '__main__':
    main() 