# Software Classifier - Python ML System

A comprehensive Python-based machine learning system for automated software inventory classification using multiple ML models, embeddings, and ensemble methods.

## 🏗️ System Architecture

The system uses a **multi-level classification approach** with fallback mechanisms:

1. **Primary Classifier**: Logistic Regression with sentence embeddings
2. **Secondary Classifier**: LightGBM with embeddings + TF-IDF features
3. **Fallback Classifier**: OpenAI LLM (optional)

### Core Data Structure

The system works with MongoDB documents in the `software_inventory` collection:

```javascript
{
  // Original AI predictions (preserved for audit trail)
  commercial_name: "Microsoft Office",
  category: "Productivity",

  // Current corrected values (used for display and filtering)
  current_commercial_name: "Microsoft 365",
  current_category: "Office Suite",

  // Review status and metadata
  review_status: "edited", // "not_reviewed" | "correct" | "edited"
  review_data: {
    reviewed_at: Date,
    reviewed_by: "<EMAIL>",
    changes_made: { /* original -> corrected values */ }
  },

  // Software inventory data
  tenant_id: "tenant_123",
  software_name: "Microsoft Office Professional",
  version: "16.0.14931",
  vendor: "Microsoft Corporation",
  // ... other inventory fields
}
```

### Key Features

- **Multi-Model Ensemble**: Combines multiple ML algorithms for robust predictions
- **Confidence Thresholds**: Automatic fallback when confidence is low
- **Resume Training**: Checkpoint system for interrupted training sessions
- **Batch Processing**: Efficient processing of large inventories
- **REST API**: Flask-based API with Swagger documentation
- **Interactive UI**: Streamlit web interface for real-time classification

## 📁 Project Structure

```
├── classifier.py                       # Core ML classifier with multi-level approach
├── run_pipeline.py                     # Command-line training and classification pipeline
├── streamlit.py                        # Interactive web UI for classification
├── software_classifier_API/
│   ├── app.py                          # Flask REST API with Swagger docs
│   └── classifier/
│       └── software_classifier.py      # API-specific classifier implementation
├── software_classifier/
│   └── sintetic_dataset.py             # Synthetic dataset generation
├── classify_tenants_inventory.py       # Tenant-specific classification
├── clean_json.py                       # Data cleaning utilities
├── migrate_s3_to_mongodb.py            # S3 to MongoDB migration
├── sample_data.py                      # Sample data generation
├── optimize_indexes.js                 # MongoDB index optimization
└── models/                             # Trained model storage (created during training)
    ├── checkpoints/                    # Training checkpoints
    ├── *.pkl                          # Serialized models
    └── *.npy                          # Embedding arrays
```

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- MongoDB database
- AWS S3 bucket (optional, for additional commercial names)

### Environment Variables

```bash
MONGODB_URI=mongodb://localhost:27017/software-classifier
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name
OPENAI_API_KEY=your_openai_key  # Optional, for LLM fallback
```

### Installation

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up MongoDB collections and indexes (if needed)
node optimize_indexes.js

# Train the model (first time setup)
python run_pipeline.py --train groundtruth.json

# Start the API server
python software_classifier_API/app.py

# Or start the Streamlit interface
streamlit run streamlit.py
```

## 📊 ML Pipeline Flow

### 1. Training Phase
```
Ground Truth Data → Text Preprocessing → Sentence Embeddings
                                      ↓
                    TF-IDF Features → Feature Engineering
                                      ↓
                    Model Training → LogisticRegression + LightGBM
                                      ↓
                    Model Persistence → Checkpoints & Serialization
```

### 2. Classification Phase
```
Raw Software Data → Text Preprocessing → Embedding Generation
                                      ↓
                    Primary Classifier (LogisticRegression)
                                      ↓
                    Confidence Check → Secondary Classifier (LightGBM)
                                      ↓
                    Final Fallback → OpenAI LLM (if configured)
                                      ↓
                    Results → {commercial_name, category, confidence}
```

### 3. API & UI Flow
```
REST API ← Flask App ← Trained Models
Streamlit UI ← Direct Model Access
Batch Processing ← Pipeline Script
```

## 🔧 API Endpoints

### Flask REST API (Port 5000)

- **POST `/classifier/predict`**: Classify both category and commercial name
- **POST `/classifier/predict/category`**: Classify only category
- **POST `/classifier/predict/comercial`**: Classify only commercial name
- **POST `/classifier/predict/batch`**: Batch classification for multiple items
- **GET `/classifier/health`**: API health check and status

### Example API Usage

```bash
# Single prediction
curl -X POST http://localhost:5000/classifier/predict \
  -H "Content-Type: application/json" \
  -d '{"name": "CrowdStrike Device Control", "vendor": "CrowdStrike, Inc."}'

# Batch prediction
curl -X POST http://localhost:5000/classifier/predict/batch \
  -H "Content-Type: application/json" \
  -d '{"items": [{"name": "Chrome", "vendor": "Google"}, {"name": "Office", "vendor": "Microsoft"}]}'
```

### Swagger Documentation

Access interactive API documentation at: `http://localhost:5000/swagger/`

## 🎯 Features

- **Multi-Level Classification**: Primary, secondary, and fallback classifiers for robust predictions
- **Ensemble Learning**: Combines LogisticRegression, LightGBM, and optional OpenAI models
- **Confidence-Based Routing**: Automatic fallback when prediction confidence is low
- **Resume Training**: Checkpoint system allows resuming interrupted training sessions
- **Batch Processing**: Efficient processing of large software inventories
- **REST API**: Flask-based API with comprehensive Swagger documentation
- **Interactive UI**: Streamlit web interface for real-time classification and feedback
- **Model Persistence**: Automatic saving and loading of trained models
- **Text Preprocessing**: Advanced text cleaning and normalization
- **Embedding Support**: Multiple sentence transformer models (MiniLM, MPNet, etc.)

## 🛠️ Usage Examples

### Command Line Pipeline

```bash
# Train models with default settings
python run_pipeline.py --train groundtruth.json

# Train with custom thresholds and model
python run_pipeline.py \
  --train groundtruth.json \
  --model all-MPNet-base-v2 \
  --primary-threshold 0.85 \
  --secondary-threshold 0.7

# Classify inventory without retraining
python run_pipeline.py \
  --skip-train \
  --test inventory_data.json \
  --output results.json

# Evaluate model performance
python run_pipeline.py \
  --train groundtruth.json \
  --evaluate
```

### Python API Usage

```python
from classifier import SoftwareClassifier

# Initialize classifier
classifier = SoftwareClassifier(
    embedding_model="all-MiniLM-L6-v2",
    primary_threshold=0.8,
    secondary_threshold=0.6
)

# Train from file
classifier.ingest_from_file("groundtruth.json")

# Classify single item
result = classifier._classify_item({
    "Vendor": "Microsoft Corporation",
    "Name": "Office Professional"
})

print(f"Category: {result['Category']}")
print(f"Commercial Name: {result['ComercialName']}")
print(f"Confidence: {result['Confidence']}")
```

## 📈 Model Performance

### Confidence Thresholds

- **Primary Threshold (0.8)**: LogisticRegression confidence threshold
- **Secondary Threshold (0.6)**: LightGBM fallback threshold
- **Below Secondary**: Falls back to OpenAI LLM (if configured)

### Training Checkpoints

The system automatically saves checkpoints during training:

```
models/checkpoints/
├── training_data.pkl      # Preprocessed training data
├── embeddings.npy         # Generated embeddings
├── meta.json             # Training metadata
├── category_logistic_model.pkl
└── commercial_name_logistic_model.pkl
```

## 🔍 Troubleshooting

### Common Issues

1. **Model loading errors**: Ensure models are trained before classification
2. **Memory issues**: Use smaller embedding models (MiniLM vs MPNet)
3. **Slow training**: Enable checkpointing and resume functionality
4. **API connection errors**: Verify Flask server is running on port 5000

### Logs

Application logs (`classifier.log`) provide detailed information about:
- Model training progress and checkpoints
- Classification confidence scores
- Embedding generation performance
- API request/response details

## 📝 Development Notes

### Model Architecture

- **Embeddings**: Uses sentence-transformers for semantic text representation
- **Feature Engineering**: Combines embeddings with TF-IDF features for LightGBM
- **Ensemble Approach**: Multiple models vote on final classification
- **Fallback Strategy**: Graceful degradation when confidence is low

### Data Requirements

- Training data should be in JSON format with `Vendor`, `Name`, `Category`, and `ComercialName` fields
- Minimum 100+ examples per category for effective training
- Text preprocessing handles special characters, accents, and normalization