{"manifest_version": "1.0", "models": [{"filename": "xgb_com.pkl", "s3_key": "models/xgb_com.pkl", "size_mb": 29, "description": "XGBoost Commercial model"}, {"filename": "xgb_cat.pkl", "s3_key": "models/xgb_cat.pkl", "size_mb": 6.0, "description": "XGBoost Category model"}, {"filename": "tfidf_vectorizer.pkl", "s3_key": "models/tfidf_vectorizer.pkl", "size_mb": 0.128, "description": "TF-IDF Vectorizer"}, {"filename": "svd_transformer.pkl", "s3_key": "models/svd_transformer.pkl", "size_mb": 2.3, "description": "SVD Transformer"}, {"filename": "rf_com.pkl", "s3_key": "models/rf_com.pkl", "size_mb": 0.001, "description": "Random Forest Commercial model"}, {"filename": "rf_cat.pkl", "s3_key": "models/rf_cat.pkl", "size_mb": 0.001, "description": "Random Forest Category model"}, {"filename": "logreg_com.pkl", "s3_key": "models/logreg_com.pkl", "size_mb": 1.5, "description": "Logistic Regression Commercial model"}, {"filename": "logreg_cat.pkl", "s3_key": "models/logreg_cat.pkl", "size_mb": 0.109, "description": "Logistic Regression Category model"}, {"filename": "lgb_com.pkl", "s3_key": "models/lgb_com.pkl", "size_mb": 16, "description": "LightGBM Commercial model"}, {"filename": "lgb_cat.pkl", "s3_key": "models/lgb_cat.pkl", "size_mb": 6.8, "description": "LightGBM Category model"}, {"filename": "label_encoder_comercial.pkl", "s3_key": "models/label_encoder_comercial.pkl", "size_mb": 0.005, "description": "Label Encoder for Commercial"}, {"filename": "label_encoder_category.pkl", "s3_key": "models/label_encoder_category.pkl", "size_mb": 0.001, "description": "Label Encoder for Category"}, {"filename": "ensemble_com.pkl", "s3_key": "models/ensemble_com.pkl", "size_mb": 92, "description": "Ensemble Commercial model"}, {"filename": "ensemble_cat.pkl", "s3_key": "models/ensemble_cat.pkl", "size_mb": 26, "description": "Ensemble Category model"}], "total_size_mb": 184.8, "notes": "metadata.json and embedding_info.json are kept in git and not downloaded from S3"}