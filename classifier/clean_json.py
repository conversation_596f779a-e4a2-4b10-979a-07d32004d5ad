import os
import json
import glob
import re
from tqdm import tqdm

def clean_and_merge_json_files():
    # Source directory
    source_dir = "exports"
    
    # Output file name
    output_file = "inventory_clean.json"
    
    # Patterns to clean language codes
    language_patterns = [
        r" - [a-z]{2}-[a-z]{2}$",       # "- en-us" at the end
        r" - [a-z]{2}$",                # "- en" at the end
        r" \([a-z]{2}-[a-z]{2}\)$",     # "(en-us)" at the end
        r" \([a-z]{2}\)$",              # "(en)" at the end
        r" – [a-z]{2}-[a-z]{2}$",       # "– en-us" with em dash
        r" – [a-z]{2}$",                # "– en" with em dash
        r" [a-z]{2}-[a-z]{2}$",         # " en-us" with just space
        r" [A-Z]{2}-[A-Z]{2}$",         # " EN-US" in uppercase
        r" [A-Z]{2}$",                  # " EN" in uppercase
    ]
    
    # Single list for all applications
    all_applications = []
    
    # Get all JSON files in the source directory
    json_files = glob.glob(os.path.join(source_dir, "**/*.json"), recursive=True)
    
    for json_file in tqdm(json_files, desc="Processing files"):
        try:
            # Read the original JSON file
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Process each item in the file
            for item in data:
                if "_source" in item and "Applications" in item["_source"]:
                    # Process each application
                    for app in item["_source"]["Applications"]:
                        # Get the original name
                        name = app.get("Name", "")
                        
                        # Save the original name for verification
                        original_name = name
                        
                        # Clean language codes from the name using patterns
                        for pattern in language_patterns:
                            name = re.sub(pattern, "", name)
                        
                        # Remove additional spaces at the end
                        name = name.strip()
                        
                        # Use a more general regular expression for difficult cases
                        name = re.sub(r'[\s\-–_]+[a-zA-Z]{2}(?:-[a-zA-Z]{2})?$', '', name).strip()
                        
                        # If the name is empty for some reason, use the original
                        if not name:
                            name = original_name
                        
                        # Create a new object with only the required fields and clean name
                        cleaned_app = {
                            "Category": "",
                            "ComercialName": "",
                            "Vendor": app.get("Vendor", ""),
                            "Name": name
                        }
                        all_applications.append(cleaned_app)
                
        except Exception as e:
            print(f"Error processing {json_file}: {str(e)}")
    
    # Create the final document structure
    final_data = [{"Applications": all_applications}]
    
    # Save all applications to a single file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(final_data, f, indent=2, ensure_ascii=False)
        print(f"File {output_file} successfully created with {len(all_applications)} applications")
    except Exception as e:
        print(f"Error saving file {output_file}: {str(e)}")

if __name__ == "__main__":
    clean_and_merge_json_files()