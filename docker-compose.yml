version: '3.8'

services:
  software-classifier-api:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
        AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
        AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-us-east-1}
        S3_BUCKET_NAME: ${S3_BUCKET_NAME}
        S3_MODELS_PREFIX: ${S3_MODELS_PREFIX:-models/}
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
    env_file:
      - .env  # Load environment variables from .env file
    volumes:
      # Optional: Mount for development (comment out for production)
      - ./app.py:/app/app.py
      - ./classifier:/app/classifier 