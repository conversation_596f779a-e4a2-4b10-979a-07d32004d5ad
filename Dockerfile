FROM python:3.10.12-slim

# Create a non-root user to avoid permission issues
RUN groupadd --gid 1000 appuser && \
  useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# Install system dependencies
RUN apt-get update && apt-get install -y \
  gcc \
  g++ \
  && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Change ownership of the app directory to appuser
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Create and activate virtual environment to avoid permission issues
ENV VIRTUAL_ENV=/home/<USER>/venv
RUN python -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Upgrade pip in virtual environment
RUN pip install --upgrade pip

# Copy requirements first for better caching
COPY --chown=appuser:appuser requirements.txt .

# Install Python dependencies in virtual environment
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code and metadata files
COPY --chown=appuser:appuser app.py .
COPY --chown=appuser:appuser classifier/ classifier/
COPY --chown=appuser:appuser models_manifest.json .

# Create models directory and copy metadata files
RUN mkdir -p models
COPY --chown=appuser:appuser models/metadata.json models/
COPY --chown=appuser:appuser models/embedding_info.json models/

# Copy model download script
COPY --chown=appuser:appuser download_models.py .

# Download models from S3 during build
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY
ARG AWS_DEFAULT_REGION=us-east-1
ARG S3_BUCKET_NAME
ARG S3_MODELS_PREFIX=models/

ENV AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
ENV AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
ENV AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
ENV S3_BUCKET_NAME=${S3_BUCKET_NAME}
ENV S3_MODELS_PREFIX=${S3_MODELS_PREFIX}

# Set environment variables to ensure CPU-only execution
ENV CUDA_VISIBLE_DEVICES=""
ENV OMP_NUM_THREADS=1

# Download models from S3
RUN python download_models.py

# Expose port
EXPOSE 5000

# Run the application
CMD ["python", "app.py"]