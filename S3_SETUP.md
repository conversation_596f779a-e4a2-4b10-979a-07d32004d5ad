# S3 Model Downloads Setup Guide

This project uses Amazon S3 to store large ML model files instead of committing them to git. This approach allows for faster git operations and more flexible model management.

## 🏗️ Architecture

- **Git Repository**: Contains code, small metadata files, and model manifest
- **Amazon S3**: Stores large model files (~185MB total)
- **Docker Build**: Downloads models from S3 during image build
- **Runtime**: Fast API startup since models are already local

## 📋 Prerequisites

1. **AWS Account** with S3 access
2. **S3 Bucket** to store your model files
3. **AWS Credentials** (Access Key + Secret Key or IAM role)

## 🔧 Setup Instructions

### 1. Create your environment file

Create a `.env` file in the project root with your AWS credentials:

```bash
# S3 Configuration for Model Downloads
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_DEFAULT_REGION=us-east-1
S3_BUCKET_NAME=your-models-bucket-name
S3_MODELS_PREFIX=models/

# Optional: Use AWS profile instead of keys
# AWS_PROFILE=your-profile-name

# Flask configuration (optional)
FLASK_ENV=development
```

### 2. Upload your models to S3

Upload all `.pkl` files from your local `models/` directory to S3. The structure should match the `models_manifest.json`:

```
your-s3-bucket/
└── models/
    ├── ensemble_com.pkl
    ├── ensemble_cat.pkl
    ├── xgb_com.pkl
    ├── xgb_cat.pkl
    ├── lgb_com.pkl
    ├── lgb_cat.pkl
    └── ... (other model files)
```

### 3. Test local model download

```bash
# Install dependencies
pip install -r requirements.txt

# Test downloading models from S3
python download_models.py
```

### 4. Build Docker image

#### Option A: Using docker-compose (recommended for local development)
```bash
docker-compose build
docker-compose up
```

#### Option B: Using Docker directly
```bash
# Build with S3 credentials
docker build \
  --build-arg AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID \
  --build-arg AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY \
  --build-arg S3_BUCKET_NAME=$S3_BUCKET_NAME \
  -t software-classifier-api .

# Run the container
docker run -p 5000:5000 software-classifier-api
```

## 📁 File Structure

```
project/
├── models/                     # Git-ignored (except metadata files)
│   ├── metadata.json          # ✅ In git (small metadata)
│   ├── embedding_info.json    # ✅ In git (small metadata)
│   └── *.pkl                  # ❌ Not in git (downloaded from S3)
├── models_manifest.json       # ✅ Model file manifest
├── download_models.py         # ✅ S3 download script
├── docker-compose.yml         # ✅ Easy local development
├── Dockerfile                 # ✅ Modified to download from S3
└── .env                       # ❌ Not in git (your credentials)
```

## 🚀 Production Deployment

For production deployments:

1. **Use IAM roles** instead of access keys when possible
2. **Set environment variables** in your deployment platform:
   - `AWS_ACCESS_KEY_ID`
   - `AWS_SECRET_ACCESS_KEY`
   - `S3_BUCKET_NAME`
   - `AWS_DEFAULT_REGION`

3. **Example deployment commands**:
```bash
# Deploy to cloud service with environment variables
docker build \
  --build-arg AWS_ACCESS_KEY_ID=$PROD_AWS_ACCESS_KEY_ID \
  --build-arg AWS_SECRET_ACCESS_KEY=$PROD_AWS_SECRET_ACCESS_KEY \
  --build-arg S3_BUCKET_NAME=$PROD_S3_BUCKET_NAME \
  -t software-classifier-api:prod .
```

## 🔍 Troubleshooting

### Models not downloading
1. Check your AWS credentials
2. Verify S3 bucket name and permissions
3. Ensure model files exist in S3 at the specified paths

### Build failures
1. Check Docker build logs for S3 errors
2. Verify all required build args are provided
3. Test `python download_models.py` locally first

### Large build times
- This is expected on first build (~185MB download)
- Subsequent builds use Docker layer caching
- Consider using a Docker registry for faster deployments

## 📊 Model Manifest

The `models_manifest.json` file contains:
- List of all model files with S3 paths
- File sizes and descriptions
- Total download size (~185MB)

To update the manifest when adding new models, edit the JSON file and upload the new models to S3. 